@import 'mixins/index.scss';

/* Button 按钮
------------------------------- */
// 第三方字体图标大小
.el-button:not(.is-circle) i.el-icon,
.el-button i.iconfont,
.el-button i.fa,
.el-button--default i.iconfont,
.el-button--default i.fa {
	font-size: 14px !important;
}

.el-button--small i.iconfont,
.el-button--small i.fa {
	font-size: 12px !important;
}
.el-button [class*='el-icon'] + span {
	margin-left: 6px;
}
/* Input 输入框、InputNumber 计数器
------------------------------- */
// 菜单搜索
.el-autocomplete-suggestion__wrap {
	max-height: 280px !important;
}

/* Form 表单
------------------------------- */
.el-form {
	// 用于修改弹窗时表单内容间隔太大问题，如系统设置的新增菜单弹窗里的表单内容
	.el-form-item:last-of-type {
		margin-bottom: 0 !important;
	}
	.el-form-item--default {
		// margin-bottom: 10px;
	}
	// 修复行内表单最后一个 el-form-item 位置下移问题
	&.el-form--inline {
		.el-form-item--large.el-form-item:last-of-type {
			margin-bottom: 22px !important;
		}

		.el-form-item--default.el-form-item:last-of-type,
		.el-form-item--small.el-form-item:last-of-type {
			margin-bottom: 18px !important;
		}
		// 修改表单项默认右边距
		.el-form-item {
			margin-right: 12px;
		}
	}

	.el-form-item .el-form-item__label .el-icon {
		margin-right: 0px;
	}
}

// 修改数字输入框默认宽度为100%
.el-input-number {
	width: 100%;
}

.el-form-item__content {
	.el-select {
		width: 100% !important;
	}
	.el-date-editor {
		width: 100% !important;
	}
}

/* Alert 警告
------------------------------- */
.el-alert {
	border: 1px solid;
}

.el-alert__title {
	word-break: break-all;
}

/* Message 消息提示
------------------------------- */
.el-message {
	min-width: unset !important;
	padding: 15px !important;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.02);
}

/* NavMenu 导航菜单
------------------------------- */
// 鼠标 hover 时颜色
.el-menu-hover-bg-color {
	// background-color: var(--next-bg-menuBarActiveColor) !important;
		background: var(--el-color-primary-light-8) !important;
		color: var(--el-color-primary) !important;
		i {
			color: var(--el-color-primary) !important;
		
			}

}

// 默认样式修改
.el-menu {
	border-right: none !important;
	width: 100%;
	>li.is-active {
		color: var(--el-color-primary) !important;
		// background: var(--el-color-primary-light-8) !important;
		background-color: unset !important;

		::after {
			content: '';
			position: absolute;

			width: 6px;
			height: 38px;
			left: -2px;
			top: 6px;
			border-radius: 20px;
			background-color: var(--el-color-primary)
		}
	}
}

.el-menu-item {
	height: 54px !important;
	line-height: 54px !important;
	justify-content: center;
	.el-menu-tooltip__trigger  {

	justify-content: center;
	}

}
.el-sub-menu__title {
	height: 54px !important;
	line-height: 54px !important;
	justify-content: center;
}

.el-menu-item,
.el-sub-menu__title {
	color: var(--next-bg-menuBarColor) !important;
}

// 修复点击左侧菜单折叠再展开时，宽度不跟随问题
.el-menu--collapse {
	width: 100% !important;
}

// 外部链接时
.el-menu-item a,
.el-menu-item a:hover,
.el-menu-item i,
.el-sub-menu__title i {
	// color: inherit;
			color: #dddff0;
	text-decoration: none;
}

// 第三方图标字体间距/大小设置
.el-menu-item .zhong-icon,
.el-sub-menu .zhong-icon {
	@include generalIcon;
	font-size: 26px !important;

}
.el-menu-item .svg-icon,
.el-sub-menu .svg-icon,
.el-menu-item .fa,
.el-sub-menu .fa {
	@include generalIcon;
	font-size: 14px !important;

}

// 水平菜单、横向菜单高亮 背景色，鼠标 hover 时，有子级菜单的背景色
.el-menu-item.is-active,
.el-sub-menu.is-active .el-sub-menu__title,
.el-sub-menu:not(.is-opened):hover .el-sub-menu__title {
	@extend .el-menu-hover-bg-color;
	i {
		color: var(--el-color-primary) !important;
	}

}

.el-menu-item:hover {
	// @extend .el-menu-hover-bg-color;
	background: var(--el-color-primary-light-8) !important;
	color: var(--el-color-primary) !important;
	i {
	color: var(--el-color-primary) !important;

	}
}

.el-sub-menu.is-active.is-opened .el-sub-menu__title {
	// background-color: unset !important;
}
.el-sub-menu.is-opened .el-sub-menu__title {
	// background-color: unset !important;
	background-color: var(--el-color-primary-light-8);
	color: var(--el-color-primary) !important;
	i {
	color: var(--el-color-primary) !important;

	}

}
// 子级菜单背景颜色
// .el-menu--inline {
// 	background: var(--next-bg-menuBar-light-1);
// }
.el-menu--inline {
	.el-menu-item.is-active {
		background: var(--el-color-primary-light-9) !important;
		color: var(--el-color-primary) !important;
	}
}
.el-sub-menu.is-active .el-sub-menu__title {
	color: var(--el-color-primary) !important;

}
.el-menu-item.is-active {
	background: var(--el-color-primary-light-9) !important;
	color: var(--el-color-primary) !important;
}

// 水平菜单、横向菜单折叠 a 标签
.el-popper.is-dark a {
	color: var(--el-color-white) !important;
	text-decoration: none;
}

// 水平菜单、横向菜单折叠背景色
.el-popper.is-pure.is-light {
	// 水平菜单
	.el-menu--vertical {
		background: var(--next-bg-menuBar);

		.el-sub-menu.is-active .el-sub-menu__title {
			color: var(--el-menu-active-color);
		}

		.el-popper.is-pure.is-light {
			.el-menu--vertical {
				.el-sub-menu .el-sub-menu__title {
					background-color: unset !important;
					color: var(--next-bg-menuBarColor);
				}

				.el-sub-menu.is-active .el-sub-menu__title {
					color: var(--el-menu-active-color);
				}
			}
		}
	}

	// 横向菜单
	.el-menu--horizontal {
		background: var(--next-bg-topBar);

		.el-menu-item,
		.el-sub-menu {
			height: 50px !important;
			line-height: 50px !important;
			color: var(--next-bg-topBarColor);

			.el-sub-menu__title {
				height: 50px !important;
				line-height: 50px !important;
				color: var(--next-bg-topBarColor);
			}

			.el-popper.is-pure.is-light {
				.el-menu--horizontal {
					.el-sub-menu .el-sub-menu__title {
						background-color: unset !important;
						color: var(--next-bg-topBarColor);
					}

					.el-sub-menu.is-active .el-sub-menu__title {
						color: var(--el-menu-active-color);
					}
				}
			}
		}

		.el-menu-item.is-active,
		.el-sub-menu.is-active .el-sub-menu__title {
			color: var(--el-menu-active-color);
		}
	}
}

// 横向菜单（经典、横向）布局
.el-menu.el-menu--horizontal {
	border-bottom: none !important;
	width: 100% !important;

	.el-menu-item,
	.el-sub-menu__title {
		height: 50px !important;
		color: var(--next-bg-topBarColor);
	}

	.el-menu-item:not(.is-active):hover,
	.el-sub-menu:not(.is-active):hover .el-sub-menu__title {
		color: var(--next-bg-topBarColor);
	}
}

/* Tabs 标签页
------------------------------- */
.el-tabs__nav-wrap::after {
	height: 1px !important;
}

/* Dropdown 下拉菜单
------------------------------- */
.el-dropdown-menu {
	list-style: none !important;
	/*修复 Dropdown 下拉菜单样式问题 2022.03.04*/
}

.el-dropdown-menu .el-dropdown-menu__item {
	white-space: nowrap;

	&:not(.is-disabled):hover {
		background-color: var(--el-dropdown-menuItem-hover-fill);
		color: var(--el-dropdown-menuItem-hover-color);
	}
}

/* Steps 步骤条
------------------------------- */
.el-step__icon-inner {
	font-size: 30px !important;
	font-weight: 400 !important;
}

.el-step__title {
	font-size: 14px;
}

/* Dialog 对话框
------------------------------- */
.el-overlay {
	overflow: hidden;

	.el-overlay-dialog {
		display: flex;
		align-items: center;
		justify-content: center;
		position: unset !important;
		width: 100%;
		height: 100%;

		.el-dialog {
			margin: 0 auto !important;
			position: absolute;

			.el-dialog__body {
				padding: 20px !important;
			}
		}
	}
}

.el-dialog__body {
	max-height: calc(90vh - 80px) !important;
	overflow-y: auto;
	overflow-x: hidden;
}

/* Card 卡片
------------------------------- */
.el-card__header {
	padding: 15px 20px;
}

// 日历
.el-calendar-table .el-calendar-day {
	height: 50px;
	padding: 0;
}

/* Table 表格 element plus 2.2.0 版本
------------------------------- */
// 表格修改默认颜色
.el-table {
	color: #000000;
	.el-button.is-text {
		padding: 0;
	}
}

/* scrollbar
------------------------------- */
.el-scrollbar__bar {
	z-index: 4;
}

/*防止页面切换时，滚动条高度不变的问题（滚动条高度非滚动条滚动高度）*/
.el-scrollbar__wrap {
	max-height: 100%;
}

.el-select-dropdown .el-scrollbar__wrap {
	overflow-x: scroll !important;
}

/*修复Select 选择器高度问题*/
.el-select-dropdown__wrap {
	max-height: 274px !important;
}

/*修复Cascader 级联选择器高度问题*/
.el-cascader-menu__wrap.el-scrollbar__wrap {
	height: 204px !important;
}

/*用于界面高度自适应（main.vue），区分 scrollbar__view，防止其它使用 scrollbar 的地方出现滚动条消失*/
.layout-container-view .el-scrollbar__view {
	height: 100%;
}

/*防止分栏布局二级菜单很多时，滚动条消失问题*/
.layout-columns-warp .layout-aside .el-scrollbar__view {
	height: unset !important;
}

/* Pagination 分页
------------------------------- */
.el-pagination__editor {
	margin-right: 8px;
}

.el-pagination {
	margin-top: 15px;
	justify-content: flex-end;
}

/*深色模式时分页高亮问题*/
.el-pagination.is-background .btn-next.is-active,
.el-pagination.is-background .btn-prev.is-active,
.el-pagination.is-background .el-pager li.is-active {
	background-color: var(--el-color-primary) !important;
	color: var(--el-color-white) !important;
}

.el-empty {
	--el-empty-padding: 0 0 !important;
}

// 防止被tailwindcss默认样式覆盖
svg {
	display: inline;
	vertical-align: baseline;
}
