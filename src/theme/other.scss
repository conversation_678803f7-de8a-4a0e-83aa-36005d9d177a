/* wangeditor 富文本编辑器
------------------------------- */
.editor-container {
	z-index: 10; // 用于 wangeditor 点击全屏时
	.w-e-toolbar {
		border: 1px solid var(--el-border-color-light, #ebeef5) !important;
		border-bottom: 1px solid var(--el-border-color-light, #ebeef5) !important;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		z-index: 2 !important;
	}
	.w-e-text-container {
		border: 1px solid var(--el-border-color-light, #ebeef5) !important;
		border-top: none !important;
		border-bottom-left-radius: 3px;
		border-bottom-right-radius: 3px;
		z-index: 1 !important;
	}
}

[data-theme='dark'] {
	// textarea - css vars
	--w-e-textarea-bg-color: var(--el-color-white) !important;
	--w-e-textarea-color: var(--el-text-color-primary) !important;

	// toolbar - css vars
	--w-e-toolbar-color: var(--el-text-color-primary) !important;
	--w-e-toolbar-bg-color: var(--el-color-white) !important;
	--w-e-toolbar-active-color: var(--el-text-color-primary) !important;
	--w-e-toolbar-active-bg-color: var(--next-color-menu-hover) !important;
	--w-e-toolbar-border-color: var(--el-border-color-light, #ebeef5) !important;

	// modal - css vars
	--w-e-modal-button-bg-color: var(--el-color-primary) !important;
	--w-e-modal-button-border-color: var(--el-color-primary) !important;
}
.el-popper__arrow:before {
	background: var(--el-popover-bg-color) !important;
	// border: 1px solid var(--next-border-color);
}
//iconfont 字体设置
.zhong-icon{
	font-size: 18px !important;
	color: var(--el-button-text-color);
}
.el-form-item__content {
	width: 180px;
}
// .el-input__suffix {
// 	position: absolute;
// 	right: 10px;
// }
//详情左侧列表
.local-list {
    width: 165px;
    height: 100%;
    float: left;
    border-right: 1px solid #e4e7ed;
    ul {
        width: 100%;
        li {
            padding-left: 10px;
            height: 40px;
            line-height: 40px;
            position: relative;
			cursor: pointer;
            p {
                width: calc(100% - 30px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            i {
                position: absolute;
                right: 10px;
                top: 0;
                color: #b8becc;
            }

        }
		//左侧菜单选中样式
        .active {
            background-color: #e5ebf3;
            color: #003f8c;
			i {
				color: #003f8c;
			}
        }
    }
}
//详情蓝色的表头
.detail-title {
	// font-weight: bold;
	height: 40px;
	line-height: 40px;
	background-color: #003f8c;
	text-align: center;
	color: #fff;
}
//可修改详情灰色的表头
.modify-title {
	font-weight: bold;
	height: 40px;
	line-height: 40px;
	background-color: #e5ebf3;
	text-align: left;
	padding-left: 25px;
}
.el-button.is-text.is-disabled i {
	// background-color: #fff;
	color: var(--el-text-color-disabled);
}
.el-tree-node>.el-tree-node__children {
	overflow: unset;
}

// 设置 el-tree 背景色
.el-tree {
	background-color: #F3F5FA !important;
}
.marker-icon {
	position: relative;
	.map-num {
		position: absolute;
		width: 100%;
		text-align: center;
		top: 5px;
		left: 0px;
		font-size: 14px;
		font-weight: 800;
		color: #003f8c;
		font-style: normal;
	}
	.map-num-red {
		position: absolute;
		width: 100%;
    	text-align: center;
		top: 5px;
		left: 0px;
		font-size: 14px;
		font-weight: 800;
		color: #003f8c;
		font-style: normal;
		color: #ff3d3d;
	}
}
.leaflet-fense-point {
    width: max-content;
    position: absolute;
    letter-spacing: 1px;
    font-size: 12px;
    border: solid 2px;
    padding: 0 8px;
    // color: var(--color-text-primary);
    color: #fff;
    border-radius: 50px;
    display: flex;
    align-items: center;

    i {
        font-size: 12px;
        margin-right: 5px;
    }
}
.leaflet-center-text {
	display: flex;
	align-items: center;
	justify-content: space-around;
}
.vehicle-marker-icon {
	border: none !important;
	background: none !important;
}
.leaflet-div-icon {
	min-width: 12px !important;
	min-height: 12px !important;
}