<template>
	<div class="layout-navbars-breadcrumb-user" :style="{ flex: layoutUserFlexNum }">
		<!-- 白色椭圆容器 -->
		<div class="user-oval-container">
			<!-- 搜索输入框 -->
			<div class="search-container">
				<el-icon class="search-icon">
					<ele-Search />
				</el-icon>
				<el-autocomplete
					v-model="state.searchQuery"
					:fetch-suggestions="menuSearch"
					:placeholder="$t('user.searchPlaceholder')"
					@select="onHandleSelect"
					@focus="onSearchFocus"
					@blur="onSearchBlur"
					@keyup.enter="onSearchEnter"
					class="search-input"
					:popper-class="'search-popper'"
					clearable
				>
					<template #default="{ item }">
						<div class="search-item">
							<SvgIcon :name="item.meta.icon" class="search-item-icon" />
							<span class="search-item-text">{{ $t(item.name) }}</span>
						</div>
					</template>
				</el-autocomplete>
			</div>

			<!-- 通知铃铛 -->
			<div class="icon-container">
				<el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="460" :persistent="false">
					<template #reference>
						<el-badge :is-dot="isDot">
							<el-icon :title="$t('user.title4')" class="icon">
								<ele-Bell />
							</el-icon>
						</el-badge>
					</template>
					<template #default>
						<UserNews ref="newsRef" />
					</template>
				</el-popover>
			</div>

			<!-- 全屏图标 -->
			<div class="icon-container" @click="onScreenfullClick">
				<i
					class="zhong-icon icon"
					:title="state.isScreenfull ? $t('user.exitFullscreen') : $t('user.fullscreen')"
					:class="!state.isScreenfull ? 'zhong-quanping1' : 'zhong-suoxiao'"
				></i>
			</div>

			<!-- 用户名下拉菜单 -->
			<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
				<span class="user-dropdown">
					{{ userInfos.user.username }}
					<el-icon class="dropdown-arrow">
						<ele-ArrowDown />
					</el-icon>
				</span>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item command="/home">{{ $t('user.dropdown1') }}</el-dropdown-item>
						<el-dropdown-item command="personal">{{ $t('user.dropdown2') }}</el-dropdown-item>
						<el-dropdown placement="right-start" trigger="hover">
							<el-dropdown-item>
								<div class="language-dropdown">
									<el-icon><ele-Globe /></el-icon>
									<span>{{ $t('user.dropdown3') }}</span>
									<el-icon class="arrow-right"><ele-ArrowRight /></el-icon>
								</div>
							</el-dropdown-item>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item
										@click="onLanguageChange('zh-cn')"
										:class="{ 'is-active': locale === 'zh-cn' }"
									>
										<div class="language-item">
											<span>中文</span>
											<el-icon v-if="locale === 'zh-cn'" class="check-icon"><ele-Check /></el-icon>
										</div>
									</el-dropdown-item>
									<el-dropdown-item
										@click="onLanguageChange('en')"
										:class="{ 'is-active': locale === 'en' }"
									>
										<div class="language-item">
											<span>English</span>
											<el-icon v-if="locale === 'en'" class="check-icon"><ele-Check /></el-icon>
										</div>
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<el-dropdown-item divided command="logOut">{{ $t('user.dropdown5') }}</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</div>

		<!-- 隐藏的组件 -->
		<Search ref="searchRef" />
		<global-websocket uri="/open/ws/info" v-if="websocketEnable" @rollback="rollback" />
		<personal-drawer ref="personalDrawerRef"></personal-drawer>
		<setting-drawer ref="settingDrawerRef"></setting-drawer>
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbUser">
import { logout } from '/@/api/login';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import screenfull from 'screenfull';
import { useI18n } from 'vue-i18n';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import other from '/@/utils/other';
import mittBus from '/@/utils/mitt';
import { Session, Local } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { useMsg } from '/@/stores/msg';
import moment from 'moment';
import { systemMessage } from '/@/api/admin/system';

// 引入组件
const GlobalWebsocket = defineAsyncComponent(() => import('/@/components/Websocket/index.vue'));
const UserNews = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/userNews.vue'));
const Search = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/search.vue'));
const PersonalDrawer = defineAsyncComponent(() => import('/@/views/admin/user/personal.vue'));
const SettingDrawer = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/setings.vue'));	

// 定义变量内容
const { locale, t } = useI18n();
const router = useRouter();
const stores = useUserInfo();
const storesThemeConfig = useThemeConfig();
const storesTagsViewRoutes = useTagsViewRoutes();
const { userInfos } = storeToRefs(stores);
const { themeConfig } = storeToRefs(storesThemeConfig);
const { tagsViewRoutes } = storeToRefs(storesTagsViewRoutes);
const searchRef = ref();
const newsRef = ref();
const personalDrawerRef = ref();
const settingDrawerRef = ref();

interface State {
	[key: string]: boolean | string | RouteItems;
	isScreenfull: boolean;
	disabledI18n: string;
	disabledSize: string;
	searchQuery: string;
	tagsViewList: RouteItems;
}

const state = reactive<State>({
	isScreenfull: false,
	disabledI18n: 'zh-cn',
	disabledSize: 'large',
	searchQuery: '',
	tagsViewList: [],
});



// 是否开启websocket
const websocketEnable = ref(import.meta.env.VITE_WEBSOCKET_ENABLE === 'true');

// 设置分割样式
const layoutUserFlexNum = computed(() => {
	let num: string | number = '';
	const { layout, isClassicSplitMenu } = themeConfig.value;
	const layoutArr: string[] = ['defaults', 'columns'];
	if (layoutArr.includes(layout) || (layout === 'classic' && !isClassicSplitMenu)) num = '1';
	else num = '';
	return num;
});
// 搜索相关函数
const onSearchEnter = () => {
	if (state.searchQuery.trim()) {
		// 如果有搜索内容，执行搜索
		const results = state.tagsViewList.filter(createFilter(state.searchQuery));
		if (results.length > 0) {
			onHandleSelect(results[0]);
		}
	}
};

const onSearchFocus = () => {
	// 初始化搜索数据
	initTageView();
};

const onSearchBlur = () => {
	// 搜索框失去焦点时的逻辑
};

// 菜单搜索数据过滤
const menuSearch = (queryString: string, cb: Function) => {
	initTageView();
	let results = queryString ? state.tagsViewList.filter(createFilter(queryString)) : [];
	cb(results);
};

// 菜单搜索过滤
const createFilter = (queryString: string) => {
	return (restaurant: RouteItem) => {
		return restaurant.path.toLowerCase().indexOf(queryString.toLowerCase()) > -1 || t(restaurant!.name!).indexOf(queryString.toLowerCase()) > -1;
	};
};

// 初始化菜单数据
const initTageView = () => {
	if (state.tagsViewList.length > 0) return false;
	tagsViewRoutes.value.map((v: RouteItem) => {
		if (!v.meta?.isHide) state.tagsViewList.push({ ...v });
	});
};

// 当前菜单选中时
const onHandleSelect = (item: RouteItem) => {
	let { path, redirect } = item;
	if (item.meta?.isLink && !item.meta?.isIframe) window.open(item.meta?.isLink);
	else if (redirect) router.push(redirect);
	else router.push(path);
	state.searchQuery = '';
};

// 全屏点击时
const onScreenfullClick = () => {
	if (!screenfull.isEnabled) {
		ElMessage.warning(t('user.fullscreenNotSupported'));
		return false;
	}
	screenfull.toggle();
	screenfull.on('change', () => {
		if (screenfull.isFullscreen) state.isScreenfull = true;
		else state.isScreenfull = false;
	});
};
// 下拉菜单点击时
const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('user.logOutTitle'),
			message: t('user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('user.logOutConfirm'),
			cancelButtonText: t('user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				await logout();
				useUserInfo().setLoginState(-1)
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				window.location.reload();
			})
			.catch(() => {});
	} else if (path === 'personal') {
		// 打开个人页面
		personalDrawerRef.value.open();
	} else if (path === 'setting') {
		// 打开设置页面
		settingDrawerRef.value.openDrawer();
	} else {
		router.push(path);
	}
};
// 菜单搜索点击 (保留用于兼容性，但现在使用内联搜索)
const onSearchClick = () => {
	// 现在搜索是内联的，这个函数保留用于兼容性
};
// 语言切换
const onLanguageChange = (lang: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalI18n = lang;
	Local.set('themeConfig', themeConfig.value);
	locale.value = lang;
	other.useTitle();
	initI18nOrSize('globalI18n', 'disabledI18n');
};
// 锁屏
const onLockClick = () => {
	themeConfig.value.isLockScreen = true;
	themeConfig.value.lockScreenTime = 0;
	Local.set('themeConfig', themeConfig.value);
};

// 初始化组件大小/i18n
const initI18nOrSize = (value: string, attr: string) => {
	state[attr] = Local.get('themeConfig')[value];
};

// 获取到消息
const rollback = (msg: string) => {
	let type = JSON.parse(msg).cmd
	if(type == 'AlarmHandler')return
	useMsg().setMsg(
		{ 
			id:JSON.parse(msg).a,
			label: 'websocket消息', 
			value: msg, 
			// time: formatAxis(new Date()),
			timeFormat: moment().format('YYYY-MM-DD HH:mm:ss'),
			state:1,
			msgLevel:JSON.parse(msg).l
			
		});
};

const isDot = computed(() => {
	return useMsg().getAllMsg().length > 0;
});
const getAllMessage = ()=>{
	systemMessage({
		size:-1,
		current:1,
		userId:userInfos.value.user.userId
	}).then((res:any) => {
		if(!res.data.records.length)return 
		let finData = res.data.records.filter(item=>{
				let type = JSON.parse(item.msgContent).cmd
				return type != 'AlarmHandler'
			}).map(item=>{
			return {
				id:JSON.parse(item.msgContent).a,
				label: 'websocket消息', 
				value: item.msgContent, 
				// time: formatAxis(new Date()),
				timeFormat: item.msgTime,
				state: item.state,
				msgLevel:item.msgLevel
			}
		})
		useMsg().setAllMsg(finData);
	});
}
// 页面加载时
onMounted(() => {
	if (Local.get('themeConfig')) {
		initI18nOrSize('globalComponentSize', 'disabledSize');
		initI18nOrSize('globalI18n', 'disabledI18n');
	}
	// useMsg().setAllMsg()
	getAllMessage()
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding-right: 15px;

	.user-oval-container {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 25px;
		padding: 8px 16px;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		gap: 12px;
		width: 400px;
		min-width: 400px;
		max-width: 400px;
		height: 48px;

		.search-container {
			display: flex;
			align-items: center;
			flex: 1;
			min-width: 200px;
			max-width: 250px;

			.search-icon {
				color: #9ca3af;
				margin-right: 12px;
				font-size: 16px;
				flex-shrink: 0;
			}

			.search-input {
				flex: 1;
				width: 100%;

				:deep(.el-autocomplete) {
					width: 100% !important;
				}

				:deep(.el-input) {
					--el-input-border-color: transparent !important;
					--el-input-hover-border-color: transparent !important;
					--el-input-focus-border-color: transparent !important;
				}

				:deep(.el-input__wrapper) {
					background-color: transparent !important;
					border: 0 !important;
					border-color: transparent !important;
					box-shadow: none !important;
					padding: 0 !important;
					width: 100% !important;
					outline: none !important;

					&:hover,
					&.is-focus,
					&.is-focused {
						background-color: transparent !important;
						border: 0 !important;
						border-color: transparent !important;
						box-shadow: none !important;
						outline: none !important;
					}
				}

				:deep(.el-input__inner) {
					background-color: transparent !important;
					border: 0 !important;
					border-color: transparent !important;
					box-shadow: none !important;
					color: #374151 !important;
					font-size: 14px !important;
					padding: 0 !important;
					height: auto !important;
					line-height: normal !important;
					outline: none !important;

					&::placeholder {
						color: #9ca3af !important;
					}

					&:focus,
					&:hover {
						background-color: transparent !important;
						border: 0 !important;
						border-color: transparent !important;
						box-shadow: none !important;
						outline: none !important;
					}
				}

				:deep(.el-input__suffix) {
					right: 0 !important;
				}

				:deep(.el-input__suffix-inner) {
					.el-input__clear {
						color: #9ca3af !important;
					}
				}
			}
		}

		.icon-container {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 32px;
			height: 32px;
			border-radius: 50%;
			cursor: pointer;
			transition: background-color 0.2s ease;

			&:hover {
				background-color: #f3f4f6;
			}

			.icon {
				color: #6b7280;
				font-size: 16px;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			// 全屏图标样式
			.zhong-icon {
				color: #6b7280;
				font-size: 16px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.user-dropdown {
			display: flex;
			align-items: center;
			color: #374151;
			cursor: pointer;
			transition: color 0.2s ease;
			font-size: 14px;
			height: 32px;
			line-height: 32px;

			&:hover {
				color: #3b82f6;
			}

			.dropdown-arrow {
				margin-left: 4px;
				font-size: 12px;
				display: flex;
				align-items: center;
			}
		}
	}
}

:deep(.el-badge) {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 32px;
}

:deep(.el-badge__content.is-fixed) {
	top: 6px;
	right: 6px;
}

.search-item {
	display: flex;
	align-items: center;
	padding: 8px 12px;

	.search-item-icon {
		margin-right: 8px;
		font-size: 14px;
	}

	.search-item-text {
		font-size: 14px;
		color: #374151;
	}
}

:deep(.search-popper) {
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

// 全局覆盖 Element Plus 输入框样式
:deep(.el-autocomplete .el-input .el-input__wrapper) {
	border: none !important;
	box-shadow: none !important;
	background: transparent !important;
}

:deep(.el-autocomplete .el-input .el-input__inner) {
	border: none !important;
	background: transparent !important;
}
</style>
