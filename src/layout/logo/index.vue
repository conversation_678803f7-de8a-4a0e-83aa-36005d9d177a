<template>
	<div class="layout-logo" v-if="setShowLogo" @click="onThemeConfigChange">
		<span :style="[logoStyle, ellipsisStyle]">{{ storesUserInfo.userInfos?.user?.username }}</span>
	</div>
	<div class="layout-logo-size" v-else @click="onThemeConfigChange">
		<img :src="logoMini" class="layout-logo-size-img" />
	</div>
</template>

<script setup lang="ts" name="layoutLogo">
import { useThemeConfig } from '/@/stores/themeConfig';
import { useUserInfo } from '/@/stores/userInfo';
import zclLogo from '/@/assets/zcl_logo.ico';
import defLogo from '/@/assets/default.png';

import hgLogo from '/@/assets/hg_logo.ico';
import defaultLogo from '/@/assets/favicon.ico';

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const storesUserInfo = useUserInfo();
const { themeConfig } = storeToRefs(storesThemeConfig);
// 根据userType设置logo样式
const logoStyle = computed(() => {
	const userType = storesUserInfo.userInfos?.user?.userType;
	return {
		color: userType != '0' ? '#fff' : 'var(--el-color-primary)'
	}
});

// 设置 logo 的显示。classic 经典布局默认显示 logo
const setShowLogo = computed(() => {
	let { isCollapse, layout } = themeConfig.value;
	return !isCollapse || layout === 'classic' || document.body.clientWidth < 1000;
});
// logo 点击实现菜单展开/收起
const onThemeConfigChange = () => {
	if (themeConfig.value.layout === 'transverse') return false;
	themeConfig.value.isCollapse = !themeConfig.value.isCollapse;
};

// 添加省略样式和内边距
const ellipsisStyle = {
	maxWidth: '220px',
	overflow: 'hidden',
	textOverflow: 'ellipsis',
	whiteSpace: 'nowrap',
	display: 'inline-block',
	padding: '0 10px'
}

// 根据userType获取对应的logo
const logoMini = computed(() => {
	const userType = storesUserInfo.userInfos?.user?.userType;
	switch (userType) {
		case '1':
			return defLogo;
		case '4':
			return defLogo;
		default:
			return defLogo;
	}
});
</script>

<style scoped lang="scss">
.layout-logo {
	width: 220px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
	font-size: 16px;
	cursor: pointer;
	animation: logoAnimation 0.3s ease-in-out;
	span {
		white-space: nowrap;
		display: inline-block;
		font-size: 21.5px;
		font-weight: 700;
		white-space: nowrap;
	}
	&:hover {
		span {
			color: var(--color-primary-light-2);
		}
	}
}
.layout-logo-size {
	width: 100%;
	height: 100px;
	display: flex;
	cursor: pointer;
	animation: logoAnimation 0.3s ease-in-out;
	&-img {
		width: 36px;
		margin: auto;
	}
	&:hover {
		img {
			animation: logoAnimation 0.3s ease-in-out;
		}
	}
}
</style>
