<template>
	<div class="head-container h-full overflow-auto w-full">
		<div class="head-container-header">
			<div class="head-container-header-input">
				<div class="search" @click="getTreeData" v-if="showSearch">
					<i class="zhong-icon zhong-shuaxin"></i>
				</div>
				<el-input v-model="searchName" prefix-icon="search" :placeholder="placeholder" clearable :style="showSearch ? 'width:calc(100% - 39px)':''"/>
			</div>
			<div class="head-container-header-dropdown flex items-center" v-if="showExpand">
				<el-dropdown :hide-on-click="false">
					<el-icon style="transform: rotate(90deg)">
						<MoreFilled />
					</el-icon>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item>
								<el-button
									:class="buttonClass"
									link
									type="primary"
									:icon="isExpand ? 'expand' : 'fold'"
									@click="toggleRowExpansionAll(isExpand ? false : true)"
								>
									{{ isExpand ? '折叠' : '展开' }}
								</el-button>
							</el-dropdown-item>
							<el-dropdown-item v-if="check">
								<el-button :class="buttonClass" link type="primary" @click="toggleCheckAll(isCheckAll?false:true)">
									<i :class="isCheckAll ? 'zhong-icon zhong-quxiaoquanxuan1' : 'zhong-icon zhong-quanxuan'"></i>
									<span class="ml-1.5">
										{{ isCheckAll ? '不全选' : '全选' }}
									</span>
								</el-button>
							</el-dropdown-item>
							<el-dropdown-item v-if="checkStrictly">
								<el-button :class="buttonClass" link type="primary" @click="handleIsCheckStrictly(isCheckStrictly ? false : true)">
									<i :class="isCheckStrictly ? 'zhong-icon zhong-guanlian' : 'zhong-icon zhong-quxiaoquanxuan1'"></i>
									<span class="ml-1.5">{{ isCheckStrictly ? '关联' : '不关联' }}</span>
								</el-button>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
		</div>
		<div class="head-container-tree overflow-auto">
			<el-tree
				class="mt10 tree-width"
				:data="state.List"
				:props="props.props"
				:expand-on-click-node="false"
				ref="treeRef"
				v-loading="state.localLoading"
				:node-key="props.props.value"
				highlight-current
				:default-expanded-keys="defaultExpandedKeys"
				@node-click="handleNodeClick"
				:show-checkbox="check"
				:check-strictly="isCheckStrictly"
				@check="handleCheckChange"
				:filter-node-method="filterNode"
			>
				<template #default="{ node, data }" v-if="$slots.default">
					<slot :node="node" :data="data"></slot>
				</template>
			</el-tree>
		</div>
	</div>
</template>

<script setup lang="ts" name="query-tree">
import { useMessage } from '/@/hooks/message';
import {removeTreeLayer} from '/@/utils/other';
const emit = defineEmits(['search', 'nodeClick', 'nodeCheck', 'checkNode', 'onLoaded', ]);
interface TreeNode{
	id: string;
	label:string;
	name:string;
	type:string;
	children: TreeNode[];
}
const props = defineProps({
	/**
	 * 树结构属性配置。
	 *
	 * @default { label: 'name', children: 'children', value: 'id' }
	 */
	props: {
		type: Object,
		default: () => {
			return {
				label: 'name',
				children: 'children',
				value: 'id',
			};
		},
	},

	/**
	 * 输入框占位符。
	 *
	 * @default ''
	 */
	placeholder: {
		type: String,
		default: '',
	},

	/**
	 * 是否显示加载中状态。
	 *
	 * @default false
	 */
	loading: {
		type: Boolean,
		default: false,
	},

	/**
	 * 查询函数，必须返回 Promise 类型数据。
	 */
	query: {
		type: [Function,Array],
		required: true,
	},

	/**
	 * 是否显示折叠控制
	 */
	showExpand: {
		type: Boolean,
		default: true,
	},
	showSearch: {
		type: Boolean,
		default: false,
	},
	/**
	 * 是否多选
	 */
	check: {
		type: Boolean,
		default: false,
	},
	/**
	 * 是否显示关联
	 */
	checkStrictly: {
		type: Boolean,
		default: false,
	},
	/**
	 * 多选模式下是否传递半选的节点
	 */
	checkHalf: {
		type: Boolean,
		default: false,
	},
	/**
	 * 是否隐藏车辆节点（车辆相关业务的部门树需要用车辆树接口隐藏车辆节点）
	 */
	isLeafHide:{
		type: Boolean,
		default: false,
	},
	/**
	 * 是否默认全选
	 *
	 * @default false
	 */
	defaultCheckAll: {
		type: Boolean,
		default: false,
	},
});

const state = reactive({
	List: [], // 树形结构列表数据
	localLoading: props.loading, // 是否加载中
	isLoaded: false,
});
const defaultExpandedKeys = ref<string[]>([]);
const treeRef = ref(); // 部门树形结构组件实例引用
const searchName = ref(); // 查询关键字
const isExpand = ref(true); // 是否展开所有节点
const isCheckStrictly = ref(false); //多选状态下父子节点是否关联
const isCheckAll = ref(false);
const buttonClass = computed(() => {
	return ['!h-[20px]', 'reset-margin', '!text-gray-500', 'dark:!text-white', 'dark:hover:!text-primary'];
});

/**
 * 点击树形结构节点触发的事件。
 *
 * @param item 被点击的节点数据。
 */
const handleNodeClick = (item: any) => {
	emit('nodeClick', item);
};
/**
 * 多选树节点事件
 *
 */
const handleCheckChange = (item: any) => {
	let ids = treeRef.value!.getCheckedKeys();
	let nodes = treeRef.value!.getCheckedNodes();
	if (props.checkHalf) {
		ids = ids.concat(treeRef.value!.getHalfCheckedKeys());
		nodes = nodes.concat(treeRef.value!.getHalfCheckedNodes());
	}
	emit('nodeCheck', ids);
	emit('checkNode', nodes);
};
watch(searchName, (val) => {
	treeRef.value!.filter(val);
});
/**'
 *筛选节点
 *@param value 筛选关键字
 *@param data 树数据
 */
const filterNode = (value: string, data: Array) => {
	if (!value) return true;
	return data.name.includes(value);
};
/**
 * 获取部门树形结构数据。
 */
const getTreeData = async () => {
	if (props.query instanceof Function) {
		state.localLoading = true;
		defaultExpandedKeys.value = [];
		try {
			const result = props.query();
			if ((typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function') {
				const response = await result;
				state.List = response.data;
				//车辆相关的部门树用车辆树去除车辆节点后的数据
				if(props.isLeafHide){
					removeTreeLayer(response.data[0],4)
				}
				//这个回调是用于初次加载树形结构后，对树形结构进行节点勾选回显的处理，需要在父组件调用这个方法进行处理节点回显
				state.isLoaded = true;
				emit('onLoaded');
				defaultExpandedKeys.value.push(state.List[0].id);
			}
		} catch (err: any) {
			useMessage().error(err.msg);
		} finally {
			state.localLoading = false;
			defaultExpandedKeys.value.push(state.List[0].id);

		}
	}else {
		state.List = props.query
		state.List.length && defaultExpandedKeys.value.push(state.List[0][props.props.value]);
		emit('onLoaded');

	}
};
/**
 * 切换所有节点的展开/收起状态。
 *
 * @param status 目标状态，true 为展开，false 为收起。
 */
const toggleRowExpansionAll = (status) => {
	isExpand.value = status;
	const nodes = treeRef.value.store._getAllNodes();
	for (let i = 0; i < nodes.length; i++) {
		nodes[i].expanded = status;
	}
};
/**
 *多选模式下，节点的全选/不全选
 *@param status 目标状态，true 为全选，false 为不全选。
 */
const toggleCheckAll = (status: boolean) => {
	isCheckAll.value = status;
	if (status) {
		treeRef.value?.setCheckedKeys(state.List.map((item) => item.id));
	} else {
		treeRef.value?.setCheckedKeys([], false);
	}
	handleCheckChange();
};
/**
 * 父子选择是否关联
 * @param status 目标状态，true 为关联，false 为取消关联。
 */
const handleIsCheckStrictly = (status) => {
	isCheckStrictly.value = status;
	treeRef.value?.setCheckedKeys([], false);
};
/**
 * 数据勾选的回显
 * @param ids
 */
const handleCheckNode = (ids: String) => {
	if (!state.isLoaded) return;
	if (props.check) {
		ids.forEach((id: String) => {
			treeRef.value?.setChecked(id, true, false);
		});
	} else {
		treeRef.value?.setCurrentKey(ids);
	}
};
onMounted(async () => {
	await getTreeData();
	if (props.defaultCheckAll) {
		toggleCheckAll(true);
	}
});
//页面销毁前初始化
const initializeTree = () => {
	isExpand.value = true;
	isCheckStrictly.value = false;
	if (props.check) {
		treeRef.value?.setCheckedKeys([], false);
	} else {
		treeRef.value?.setCurrentKey(null, false);
	}
};

// 方便父组件调用刷新树方法/初始化树
defineExpose({
	getTreeData,
	initializeTree,
	handleCheckNode,
	treeRef
});
</script>
<style lang="scss" scoped>
.search {
	width: 32px;
	height: 32px;
	float: left;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	margin-right: 5px;
	line-height: 30px;
	text-align: center;
	cursor: pointer;
}
.head-container {
	&-header {
		display: flex;
		align-items: center;
		&-input {
			width: 90%;
		}
		&-dropdown {
			flex: 1;
			margin-left: 5%;
		}
	}
	&-tree {
		height: 95%;
	}
}
.tree-width{
		width: fit-content;
    min-width: 200px;
	}
</style>
