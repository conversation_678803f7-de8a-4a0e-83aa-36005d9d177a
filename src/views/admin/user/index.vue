<template>
	<div class="layout-padding">
		<splitpanes>
			<!-- <pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-scrollbar>
						<query-tree :placeholder="$t('common.queryDeptTip')" :query="deptData.queryList" :show-expand="true" @nodeCheck="handleNodeCheck" :check="true" checkStrictly>
							<template #default="{ node, data }">
								<el-tooltip v-if="data.isLock" class="item" effect="dark" :content="$t('sysuser.noDataScopeTip')" placement="right-start">
									<span
										>{{ node.label }}
										<SvgIcon name="ele-Lock" />
									</span>
								</el-tooltip>
								<span v-if="!data.isLock">{{ node.label }}</span>
							</template>
						</query-tree>
					</el-scrollbar>
				</div>
			</pane> -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row v-show="showSearch">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList" style="flex:1">
							<el-form-item :label="$t('sysuser.username')" prop="username">
								<el-input v-model="state.queryForm.username" :placeholder="$t('sysuser.inputUsernameTip')" clearable />
							</el-form-item>
							<el-form-item :label="$t('sysuser.phone')" prop="phone">
								<el-input v-model="state.queryForm.phone" :placeholder="$t('sysuser.inputPhoneTip')" clearable />
							</el-form-item>
							<el-form-item label="到期日期" prop="filterAnnual">
								<el-select v-model="state.queryForm.filterAnnual" clearable>
									<el-option v-for="item in filterAnnualList" :key="item.value" :value="item.value" :label="item.label"></el-option>
								</el-select>
							</el-form-item>
						</el-form>
						<div class="search-button">
							<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							<el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
						</div>
					</el-row>
					<el-row>
						<div class="mb8" style="width: 100%">
							<el-button v-auth="'sys_user_add'" icon="folder-add" type="primary" @click="userDialogRef.openDialog()">
								{{ $t('common.addBtn') }}
							</el-button>
							<el-button plain class="ml10" icon="upload-filled" type="primary" @click="excelUploadRef.show()">
								{{ $t('common.importBtn') }}
							</el-button>
							<el-button plain  class="ml10" icon="Download" type="primary" @click="exportExcel">
								导出
							</el-button>
							<el-button
								plain
								v-auth="'sys_user_del'"
								:disabled="multiple"
								class="ml10"
								icon="Delete"
								type="primary"
								@click="handleDelete(selectObjs)"
							>
								{{ $t('common.delBtn') }}
							</el-button>
							<right-toolbar
								class="ml10"
								style="float: right; margin-right: 20px"
								:toolShow="false"
								v-model:checkedList="tableColumnList"
								v-model:showSearch="showSearch"
								:list="allTableColumnList"
								pageKey="userMgt"

							></right-toolbar>
						</div>
					</el-row>
					<el-table
						v-loading="state.loading"
						:data="state.dataList"
						@selection-change="handleSelectionChange"
						border
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column :selectable="handleSelectable" type="selection" width="40" />
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column :label="$t('common.action')" width="120" fixed="left">
							<template #default="scope">
								<el-button v-auth="'sys_user_edit'" icon="edit-pen" text type="primary" @click="userDialogRef.openDialog(scope.row.userId)">
								</el-button>
								<el-tooltip :content="$t('sysuser.deleteDisabledTip')" :disabled="scope.row.userId !== '1'" placement="top">
									<span style="margin-left: 12px">
										<el-button
											icon="delete"
											v-auth="'sys_user_del'"
											:disabled="scope.row.username === 'admin'"
											text
											type="primary"
											@click="handleDelete([scope.row.userId])"
											>
										</el-button>
									</span>
								</el-tooltip>
								<el-button icon="DocumentCopy" text type="primary" 
								v-auth="'sys_user_add'"
								@click="userDialogRef.openDialog(scope.row.userId,'copy')" style="margin-left: 12px">

								</el-button>
                <!-- 登录链接生成 -->
								<el-button icon="Setting" text type="primary" v-auth="'jump_secret_gen'" @click="jumpSecretGenerate(scope.row.username)" style="margin-left: 12px"> </el-button>
							</template>
						</el-table-column>
						<el-table-column
							v-for="item in tableColumnList"
							:prop="item.prop"
							:label="item.label"
							show-overflow-tooltip
							:key="item.prop"
							:sortable="item.sortable"
							:min-width="item.width"
						>
							<template #default="scope">
								<span v-if="item.prop == 'role'">
									<el-tag v-for="(item, index) in scope.row.roleList" :key="index">{{ item.roleName }}</el-tag>
								</span>
								<span v-else-if="item.prop == 'lock'">
									<el-switch v-model="scope.row.lockFlag" @change="changeSwitch(scope.row)" active-value="0" inactive-value="9"></el-switch>
								</span>
								<span v-else-if="item.prop == 'userType'">{{scope.row.userType ? showUserObj[scope.row.userType]?showUserObj[scope.row.userType].label:'' : ''}}</span>
								<span v-else-if="item.prop == 'validDate'">
									<el-tag type="warning" v-if="scope.row.validDate && !isAfterToday(scope.row.validDate)">已到期</el-tag>

									{{ scope.row.validDate}}
								</span>
								
								<span v-else>{{ scope.row[item.prop] }}</span>
							</template>
						</el-table-column>
					</el-table>
					<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
				</div>
			</pane>
		</splitpanes>

		<user-form ref="userDialogRef" @refresh="getDataList(false)" />

		<upload-excel
			ref="excelUploadRef"
			:title="$t('sysuser.importUserTip')"
			temp-url="admin/user.xlsx"
			url="/admin/user/import"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj, jump } from '/@/api/admin/user';
import { deptTree } from '/@/api/admin/dept';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { allTableColumnList } from './userTableConfig';
import { useDict } from '/@/hooks/dict';
import { computed } from 'vue';
import commonFunction from "/@/utils/commonFunction";
const { copyText } = commonFunction();
// 动态引入组件
const UserForm = defineAsyncComponent(() => import('./form.vue'));
const QueryTree = defineAsyncComponent(() => import('/@/components/QueryTree/index.vue'));

const filterAnnualList = reactive([
	{
		value:0,
		label:'全部'
	},
	{
		value:1,
		label:'已到期'
	},
	{
		value:2,
		label:'3天内到期'
	},
	{
		value:3,
		label:'10天内到期'
	},
	{
		value:4,
		label:'30天内到期'
	},
])
const { t } = useI18n();
const tableColumnList = ref<any[]>([]);
const { user_type } = useDict('user_type');
const showUserObj = computed(()=>{
	let obj = {}
	user_type.value.forEach((item:any) => {
			obj[item.value] = item
		});
	return obj
})
// const showUserObj = reactive({})
onMounted(async () => {
	tableColumnList.value = window.localStorage.getItem('userMgs') ? JSON.parse(window.localStorage.getItem('userMgs')) : allTableColumnList.filter((item) => item.checked);
});
watch(
	() => tableColumnList.value,
	(newVal) => {
		state.exportParams.headers = newVal.map((item) => `${item.label}@${item.prop}@10000@000000`);
	}
);
const isAfterToday = (time: string)=> {
      const today = new Date();
      const date = new Date(time);
      return date > today;
    }
// 定义变量内容
const userDialogRef = ref();
const excelUploadRef = ref();
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptIds: [],
		username: '',
		phone: '',
		filterAnnual:0
	},
	pageList: pageList,
	exportParams: {
		sheetName: '用户列表',
		title: '用户列表',
		headers: [],
		dataList: [],
	},
	exportSpecialHandle: (row: Object) => {
		row.lock = (row.lockFlag == '0' ? '正常' : '禁用');
		row.role = row.roleList.map((item: any) => item.roleName).join(',');
		row.userType = (row.userType ? showUserObj.value[row.userType] ? showUserObj.value[row.userType].label:'' : '')
		return row;
	},
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 部门树使用的数据
const deptData = reactive({
	queryList: (name: String) => {
		return deptTree({
			deptName: name,
		});
	},
});
// //  table hook
// const { downBlobFile, getDataList, currentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// // 导出excel
// const exportExcel = () => {
// 	downBlobFile(state.exportParams, '操作日志.xlsx');
// };
// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	state.queryForm.deptId = '';
	getDataList();
};

// 点击树
const handleNodeCheck = (data: any) => {
	
	state.queryForm.deptIds = data;
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile(state.queryForm, '用户列表.xlsx');
};

// 是否可以多选
const handleSelectable = (row: any) => {
	return row.username !== 'admin';
};

// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// 生成登录链接
const jumpSecretGenerate = async (username: string) => {
  try {
    // 0：获取密码，1：获取完整链接
    const secret = await jump(username, 0);
    // 将 secret 复制到剪贴板
    copyText(secret.data);
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};

//表格内开关 (用户状态)
const changeSwitch = async (row: object) => {
	await putObj(Object.assign(row,{
		password:undefined,
		groupManager: row.groupManager ? 1 : 0,
		role: row.roleList.map((item) => item.roleId)
	}));
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};
</script>
<style scoped lang="scss">
:deep(.el-form-item__content) {
	width: 190px;
}
.search-button {
	width: 172px;
}
</style>